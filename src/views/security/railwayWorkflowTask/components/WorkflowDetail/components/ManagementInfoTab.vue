<template>
  <div class="management-info-content">
    <div v-if="data">
      <Description
        :schema="descSchema"
        :data="data"
        :column="2"
        :bordered="true"
        size="small"
        :use-collapse="false"
      />
    </div>
    <div v-else class="empty-state">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, h } from 'vue';
  import { Empty as AEmpty, Button as AButton } from 'ant-design-vue';
  import { Description, type DescItem } from '@/components/Description';
  import { STATUS_TEXT_MAP } from '../constants';
  import type { ManagementInfoData } from '../types';

  defineOptions({ name: 'ManagementInfoTab' });

  interface Props {
    data: ManagementInfoData | null;
    loading: boolean;
  }

  interface Emits {
    (e: 'previewImage', imageUrl: string): void;
  }

  defineProps<Props>();
  const emit = defineEmits<Emits>();

  /**
   * 描述列表配置
   */
  const descSchema = computed<DescItem[]>(() => [
    {
      field: 'remark',
      label: '异常描述',
      render: (value) => value || '-'
    },
    {
      field: 'responsiblePerson',
      label: '责任人',
      render: (value) => value || '-'
    },
    {
      field: 'occurrenceTime',
      label: '发生时间',
      render: (value) => value || '-'
    },
    {
      field: 'status',
      label: '状态',
      render: (value) => getStatusText(value)
    },
    {
      field: 'stationName',
      label: '车站名称',
      render: (value) => value || '-'
    },
    {
      field: 'annexUrl',
      label: '附件图片',
      render: (value) => {
        if (value) {
          return h(AButton, {
            type: 'link',
            size: 'small',
            onClick: () => emit('previewImage', value)
          }, () => '查看图片');
        }
        return '-';
      }
    }
  ]);

  /**
   * 获取状态文本
   */
  function getStatusText(status?: string) {
    if (!status) return '-';
    return STATUS_TEXT_MAP[status as keyof typeof STATUS_TEXT_MAP] || '-';
  }
</script>

<style scoped>
.management-info-content {
  /* padding: 16px 0; */
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}
</style>

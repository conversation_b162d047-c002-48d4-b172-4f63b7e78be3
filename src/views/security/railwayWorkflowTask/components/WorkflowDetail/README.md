# WorkflowDetail 组件模块

这个模块包含了工作流详情弹窗的所有相关功能，已经按照功能进行了模块化拆分。

## 文件结构

```
WorkflowDetail/
├── README.md                           # 说明文档
├── index.ts                           # 统一导出文件
├── types.ts                           # TypeScript 类型定义
├── constants.ts                       # 常量配置
├── components/                        # 子组件
│   ├── FaceRecognitionTab.vue        # 人员清点Tab组件
│   ├── ManagementInfoTab.vue         # 管理信息Tab组件（着装检查、穿戴检查、接地线）
│   └── UtensilRecognitionTab.vue     # 工具清点Tab组件
├── composables/                       # 可复用逻辑
│   ├── useWorkflowDetailData.ts      # 数据管理逻辑
│   └── useImagePreview.ts            # 图片预览逻辑
└── utils/                            # 工具函数
    └── tabUtils.ts                   # Tab相关工具函数
```

## 模块说明

### 1. 类型定义 (types.ts)
- `WorkflowStep`: 工作流步骤接口
- `TabItem`: Tab项接口
- `FaceRecognitionData`: 人脸识别数据接口
- `ManagementInfoData`: 管理信息数据接口
- `UtensilRecognitionData`: 工具识别数据接口
- `TableColumn`: 表格列定义类型

### 2. 常量配置 (constants.ts)
- `TAB_LABEL_MAP`: 标签映射关系
- `EVENT_TYPE_MAP`: 事件类型映射关系
- `MANAGEMENT_INFO_TABS`: 管理信息相关的Tab类型
- `UTENSIL_TABS`: 工具清点相关的Tab类型
- `UPPER_UTENSIL_COLUMNS`: 工具清点（上道）表格列定义
- `LOWER_UTENSIL_COLUMNS`: 工具清点（上下道）表格列定义
- `STATUS_TEXT_MAP`: 状态文本映射
- `FACE_STATUS_MAP`: 人脸识别状态映射
- `UTENSIL_STATUS_MAP`: 工具清点状态映射（1: 一致, 2: 不一致）

### 3. 子组件

#### FaceRecognitionTab.vue
- 功能：显示人员清点数据
- Props：`data`, `loading`
- Events：`preview-image`
- 特性：状态图标显示、检测图片查看

#### ManagementInfoTab.vue
- 功能：显示管理信息（着装检查、穿戴检查、接地线）
- Props：`data`, `loading`
- Events：`preview-image`
- 特性：使用 `Description` 组件展示、状态文本映射、动态渲染按钮
- 技术栈：`@/components/Description`、Vue 3 Composition API

#### UtensilRecognitionTab.vue
- 功能：显示工具清点数据
- Props：`data`, `columns`, `loading`, `isLowerUtensil`
- Events：`preview-image`
- 特性：使用 `BasicTable` 和 `TableAction` 组件、动态列配置、状态文字转换（1: 一致, 2: 不一致）
- 技术栈：`@/components/Table`、Vue 3 Composition API

### 4. Composables

#### useWorkflowDetailData.ts
- 功能：管理工作流详情数据的加载和状态
- 返回值：
  - `loading`: 加载状态
  - `faceRecognitionData`: 人脸识别数据
  - `managementInfoData`: 管理信息数据
  - `utensilRecognitionData`: 工具识别数据
  - `loadTabData`: 加载Tab数据方法
  - `clearAllData`: 清空所有数据方法

#### useImagePreview.ts
- 功能：管理图片预览功能
- 返回值：
  - `previewImages`: 预览图片列表
  - `previewVisible`: 预览可见状态
  - `previewImage`: 预览单张图片方法
  - `previewMultipleImages`: 预览多张图片方法
  - `closePreview`: 关闭预览方法

### 5. 工具函数 (utils/tabUtils.ts)
- `parseDetectionItemToTabs`: 解析检测项生成Tab列表
- `getFirstTabKey`: 获取第一个有效的Tab Key
- `isFaceRecognitionTab`: 判断是否为人员清点Tab
- `isManagementInfoTab`: 判断是否为管理信息Tab
- `isUtensilRecognitionTab`: 判断是否为工具清点Tab
- `isLowerUtensilTab`: 判断是否为上下道工具清点Tab

## 使用方式

### 在主组件中使用
```typescript
import { 
  parseDetectionItemToTabs, 
  useWorkflowDetailData, 
  useImagePreview 
} from './WorkflowDetail';

// 或者单独导入
import FaceRecognitionTab from './WorkflowDetail/components/FaceRecognitionTab.vue';
```

### 扩展新的Tab类型
1. 在 `constants.ts` 中添加新的映射关系
2. 在 `utils/tabUtils.ts` 中添加判断函数
3. 创建新的Tab组件
4. 在 `useWorkflowDetailData.ts` 中添加数据加载逻辑
5. 在主组件中添加条件渲染

## 优势

1. **模块化**: 每个功能都有独立的文件，便于维护
2. **可复用**: Composables可以在其他组件中复用
3. **类型安全**: 完整的TypeScript类型定义
4. **易扩展**: 新增Tab类型只需要添加对应的组件和逻辑
5. **职责分离**: 数据逻辑、UI组件、工具函数分离
6. **易测试**: 每个模块都可以独立测试

## 最新重构改进

### 使用项目标准组件

#### 1. **UtensilRecognitionTab 重构**
- **替换组件**：从 `ant-design-vue` 的 `a-table` 替换为项目的 `BasicTable`
- **操作按钮**：使用 `TableAction` 组件统一管理表格操作
- **优势**：
  - 统一的表格样式和交互
  - 更好的类型支持
  - 内置的权限控制和状态管理
  - 更灵活的操作按钮配置

#### 2. **ManagementInfoTab 重构**
- **替换组件**：从 `ant-design-vue` 的 `a-descriptions` 替换为项目的 `Description`
- **配置化**：使用 `DescItem[]` 配置描述列表
- **优势**：
  - 统一的描述列表样式
  - 更强大的渲染功能
  - 支持动态显示/隐藏
  - 更好的响应式布局

### 技术改进点

1. **类型安全**：使用项目标准的 `BasicColumn` 和 `DescItem` 类型
2. **组件复用**：遵循项目的组件使用规范
3. **样式统一**：与项目整体UI风格保持一致
4. **功能增强**：利用项目组件的高级功能（如权限控制、响应式等）
5. **维护性**：减少对第三方组件的直接依赖，便于统一升级和维护

### 代码对比

**重构前（ant-design-vue）：**
```vue
<a-table :columns="columns" :data-source="data">
  <template #bodyCell="{ column, record }">
    <a-button @click="handleClick">操作</a-button>
  </template>
</a-table>
```

**重构后（项目组件）：**
```vue
<BasicTable :columns="tableColumns" :data-source="data">
  <template #bodyCell="{ column, record }">
    <TableAction :actions="getTableActions(record)" />
  </template>
</BasicTable>
```

这种重构方式确保了代码与项目架构的一致性，提升了整体的可维护性和用户体验。
